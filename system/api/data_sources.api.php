<?php
namespace api\data_sources;

use system\data_source_manager;
use edge\edge;
use Exception;

/**
 * Data Sources API
 *
 * Provides API endpoints for managing data sources
 */

/**
 * Get all available database tables
 */
function get_tables($params = []) {
    try {
        $tables = data_source_manager::get_available_tables();

        return [
            'success' => true,
            'data' => $tables,
            'count' => count($tables)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get table information and sample data
 */
function get_table_info($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        $table_info = data_source_manager::get_table_info($table_name);
        if (!$table_info) {
            throw new Exception('Table not found or inaccessible');
        }

        $sample_data = data_source_manager::get_sample_data($table_name, 5);

        return [
            'success' => true,
            'table_info' => $table_info,
            'sample_data' => $sample_data
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get all configured data sources
 */
function get_data_sources($params = []) {
    try {
        $data_sources = data_source_manager::get_data_sources();

        return [
            'success' => true,
            'data' => $data_sources,
            'count' => count($data_sources)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Create a new data source (API endpoint)
 */
function create_data_source($params) {
    try {
        // Validate required fields
        $required_fields = ['name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Handle both single table (legacy) and multi-table formats
        $tables = [];
        if (isset($params['selected_tables'])) {
            $tables = json_decode($params['selected_tables'], true) ?: [];
        } elseif (!empty($params['table_name'])) {
            $tables = [$params['table_name']];
        }

        if (empty($tables)) {
            throw new Exception("At least one table must be selected");
        }

        // Process joins from form data
        $joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            foreach ($params['joins'] as $join_data) {
                if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    $joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'left_table' => $join_data['left_table'],
                        'left_column' => $join_data['left_column'],
                        'right_table' => $join_data['right_table'],
                        'right_column' => $join_data['right_column'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        // Process selected columns - handle both array and JSON formats
        $selected_columns = [];
        $selected_columns_array = [];
        if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
            // Handle array format from form
            $selected_columns_array = $params['selected_columns'];
        } elseif (isset($params['selected_columns_json'])) {
            // Handle JSON format for backward compatibility
            $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
        }

        // Convert array format to table.column format
        foreach ($selected_columns_array as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        // Process filters
        $filters = [];
        if (isset($params['filters']) && is_array($params['filters'])) {
            foreach ($params['filters'] as $filter) {
                if (!empty($filter['column']) && !empty($filter['operator'])) {
                    $filters[] = [
                        'column' => $filter['column'],
                        'operator' => $filter['operator'],
                        'value' => $filter['value'] ?? ''
                    ];
                }
            }
        }

        // Process table aliases
        $table_aliases = [];
        if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
            foreach ($params['table_aliases'] as $table_name => $alias) {
                if (!empty($alias) && in_array($table_name, $tables)) {
                    $table_aliases[$table_name] = trim($alias);
                }
            }
        }

        // Process column aliases
        $column_aliases = [];
        if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
            foreach ($params['column_aliases'] as $column_key => $alias) {
                if (!empty($alias) && strpos($column_key, '.') !== false) {
                    list($table, $column) = explode('.', $column_key, 2);
                    if (in_array($table, $tables)) {
                        $column_aliases[$column_key] = trim($alias);
                    }
                }
            }
        }

        // Process custom columns
        $custom_columns = [];
        if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
            foreach ($params['custom_columns'] as $custom_column) {
                if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                    $custom_columns[] = [
                        'sql' => trim($custom_column['sql']),
                        'alias' => trim($custom_column['alias'])
                    ];
                }
            }
        }

        // Process sorting
        $sorting = [];
        if (isset($params['sorting']) && is_array($params['sorting'])) {
            foreach ($params['sorting'] as $sort) {
                if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                    $sorting[] = [
                        'column' => trim($sort['column']),
                        'direction' => strtoupper(trim($sort['direction']))
                    ];
                }
            }
        }

        // Process limits
        $limits = [
            'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
            'limit' => '',
            'offset' => ''
        ];
        if ($limits['enabled']) {
            if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
                $limits['limit'] = (int)$params['limits']['limit'];
            }
            if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
                $limits['offset'] = (int)$params['limits']['offset'];
            }
        }

        // Prepare data source configuration
        $config = [
            'name' => $params['name'],
            'table_name' => $tables[0], // Primary table for backward compatibility
            'description' => $params['description'] ?? '',
            'category' => $params['category'] ?? 'other',
            'tables' => $tables,
            'joins' => $joins,
            'selected_columns' => $selected_columns,
            'table_aliases' => $table_aliases,
            'column_aliases' => $column_aliases,
            'custom_columns' => $custom_columns,
            'sorting' => $sorting,
            'limits' => $limits,
            'column_mapping' => $params['column_mapping'] ?? [],
            'filters' => $filters
        ];

        $data_source_id = data_source_manager::create_data_source($config);

        // Return success response with redirect
        header('HX-Redirect: ' . APP_ROOT . '/data_sources');
        return [
            'success' => true,
            'data_source_id' => $data_source_id,
            'message' => 'Data source created successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Update an existing data source
 */
function update($params) {
    try {
        $data_source_id = $params['id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Get existing data source
        $existing = data_source_manager::get_data_source($data_source_id);
        if (!$existing) {
            throw new Exception('Data source not found');
        }

        // Validate required fields
        $required_fields = ['name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Handle both single table (legacy) and multi-table formats
        $tables = [];
        if (isset($params['selected_tables'])) {
            $tables = json_decode($params['selected_tables'], true) ?: [];
        } elseif (!empty($params['table_name'])) {
            $tables = [$params['table_name']];
        }

        if (empty($tables)) {
            throw new Exception("At least one table must be selected");
        }

        // Process joins from form data
        $joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            foreach ($params['joins'] as $join_data) {
                if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    $joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'left_table' => $join_data['left_table'],
                        'left_column' => $join_data['left_column'],
                        'right_table' => $join_data['right_table'],
                        'right_column' => $join_data['right_column'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        // Process selected columns - handle both array and JSON formats
        $selected_columns = [];
        $selected_columns_array = [];
        if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
            // Handle array format from form
            $selected_columns_array = $params['selected_columns'];
        } elseif (isset($params['selected_columns_json'])) {
            // Handle JSON format for backward compatibility
            $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
        }

        // Convert array format to table.column format
        foreach ($selected_columns_array as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        // Process filters
        $filters = [];
        if (isset($params['filters']) && is_array($params['filters'])) {
            foreach ($params['filters'] as $filter) {
                if (!empty($filter['column']) && !empty($filter['operator'])) {
                    $filters[] = [
                        'column' => $filter['column'],
                        'operator' => $filter['operator'],
                        'value' => $filter['value'] ?? ''
                    ];
                }
            }
        }

        // Process table aliases
        $table_aliases = [];
        if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
            foreach ($params['table_aliases'] as $table_name => $alias) {
                if (!empty($alias) && in_array($table_name, $tables)) {
                    $table_aliases[$table_name] = trim($alias);
                }
            }
        }

        // Process column aliases
        $column_aliases = [];
        if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
            foreach ($params['column_aliases'] as $column_key => $alias) {
                if (!empty($alias) && strpos($column_key, '.') !== false) {
                    list($table, $column) = explode('.', $column_key, 2);
                    if (in_array($table, $tables)) {
                        $column_aliases[$column_key] = trim($alias);
                    }
                }
            }
        }

        // Process custom columns
        $custom_columns = [];
        if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
            foreach ($params['custom_columns'] as $custom_column) {
                if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                    $custom_columns[] = [
                        'sql' => trim($custom_column['sql']),
                        'alias' => trim($custom_column['alias'])
                    ];
                }
            }
        }

        // Process sorting
        $sorting = [];
        if (isset($params['sorting']) && is_array($params['sorting'])) {
            foreach ($params['sorting'] as $sort) {
                if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                    $sorting[] = [
                        'column' => trim($sort['column']),
                        'direction' => strtoupper(trim($sort['direction']))
                    ];
                }
            }
        }

        // Process limits
        $limits = [
            'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
            'limit' => '',
            'offset' => ''
        ];
        if ($limits['enabled']) {
            if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
                $limits['limit'] = (int)$params['limits']['limit'];
            }
            if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
                $limits['offset'] = (int)$params['limits']['offset'];
            }
        }

        // Prepare update data
        $update_data = [
            'name' => $params['name'],
            'description' => $params['description'] ?? '',
            'category' => $params['category'] ?? 'other',
            'table_name' => $tables[0], // Primary table for backward compatibility
            'tables' => json_encode($tables),
            'joins' => json_encode($joins),
            'selected_columns' => json_encode($selected_columns),
            'table_aliases' => json_encode($table_aliases),
            'column_aliases' => json_encode($column_aliases),
            'custom_columns' => json_encode($custom_columns),
            'sorting' => json_encode($sorting),
            'limits' => json_encode($limits),
            'filters' => json_encode($filters),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Convert column_mapping to JSON if provided
        if (isset($params['column_mapping']) && is_array($params['column_mapping'])) {
            $update_data['column_mapping'] = json_encode($params['column_mapping']);
        }

        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $data_source_id)->update($update_data);

        // Return success response with redirect
        header('HX-Redirect: ' . APP_ROOT . '/data_sources');
        return [
            'success' => true,
            'message' => 'Data source updated successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Delete a data source
 */
function delete($params) {
    try {
        $data_source_id = $params['id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Check if data source exists
        $existing = data_source_manager::get_data_source($data_source_id);
        if (!$existing) {
            throw new Exception('Data source not found');
        }

        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $data_source_id)->delete();

        return [
            'success' => true,
            'message' => 'Data source deleted successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data from a data source with filtering and pagination
 */
function get_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Prepare criteria
        $criteria = [];
        if (!empty($params['search'])) {
            $criteria['search'] = $params['search'];
        }
        if (!empty($params['limit'])) {
            $criteria['limit'] = (int)$params['limit'];
        }
        if (!empty($params['offset'])) {
            $criteria['offset'] = (int)$params['offset'];
        }
        if (!empty($params['sort_column'])) {
            $criteria['sort_column'] = $params['sort_column'];
            $criteria['sort_direction'] = $params['sort_direction'] ?? 'asc';
        }

        $result = data_source_manager::get_data_source_data($data_source_id, $criteria);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get preview data from a data source (API endpoint)
 */
function get_preview_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;
        $limit = $params['limit'] ?? 5;

        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get sample data from a table (for new data source creation)
 */
function sample_data($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        $limit = $params['limit'] ?? 5;

        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        $result = data_source_manager::get_sample_data($table_name, $limit);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test data source connection and configuration
 */
function test_connection($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;

        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Get data source
        $data_source = data_source_manager::get_data_source($data_source_id);
        if (!$data_source) {
            throw new Exception('Data source not found');
        }

        // Test by getting a small sample
        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);

        if ($result['success']) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data_source' => $data_source,
                'sample_count' => $result['count']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Connection failed: ' . $result['error']
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data source statistics
 */
function get_stats($params) {
    try {
        $data_sources = data_source_manager::get_data_sources();
        $available_tables = data_source_manager::get_available_tables();

        // Count by category
        $categories = [];
        foreach ($data_sources as $source) {
            $category = $source['category'] ?? 'other';
            if (!isset($categories[$category])) {
                $categories[$category] = 0;
            }
            $categories[$category]++;
        }

        // Count by status
        $statuses = [];
        foreach ($data_sources as $source) {
            $status = $source['status'] ?? 'active';
            if (!isset($statuses[$status])) {
                $statuses[$status] = 0;
            }
            $statuses[$status]++;
        }

        return [
            'success' => true,
            'stats' => [
                'total_data_sources' => count($data_sources),
                'total_available_tables' => count($available_tables),
                'categories' => $categories,
                'statuses' => $statuses
            ]
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Show create data source view
 */
function create_view($params) {

    $selected_table = $params['table'] ?? '';

    // Get available tables for the form
    $available_tables = data_source_manager::get_available_tables();

    // Filter operators
    $filter_operators = [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater Than',
        '>=' => 'Greater Than or Equal',
        '<' => 'Less Than',
        '<=' => 'Less Than or Equal',
        'LIKE' => 'Contains',
        'NOT LIKE' => 'Does Not Contain',
        'IN' => 'In List',
        'NOT IN' => 'Not In List',
        'IS NULL' => 'Is Empty',
        'IS NOT NULL' => 'Is Not Empty'
    ];

    // Category options
    $category_options = [
        'users' => 'Users & Accounts',
        'products' => 'Products & Inventory',
        'orders' => 'Orders & Transactions',
        'content' => 'Content & Media',
        'analytics' => 'Analytics & Reports',
        'system' => 'System & Configuration',
        'other' => 'Other'
    ];

    // Create empty data source for new form
    $data_source = [
        'name' => '',
        'table_name' => $selected_table,
        'tables' => $selected_table ? [$selected_table] : [],
        'description' => '',
        'category' => 'other',
        'filters' => [],
        'joins' => [],
        'selected_columns' => []
    ];

    return Edge::render('data-source-builder', [
        'title' => 'Create Data Source',
        'description' => 'Create a new database data source for tables and email campaigns',
        'mode' => 'create',
        'data_source' => $data_source,
        'available_tables' => $available_tables,
        'filter_operators' => $filter_operators,
        'category_options' => $category_options,
        'redirect_url' => APP_ROOT . '/data_sources'
    ]);
}

/**
 * Show edit data source view
 */
function edit_view($params) {

    $id = $params['id'] ?? null;
    if (!$id) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source ID is required'
        ]);
    }

    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source not found'
        ]);
    }

    // Get available tables for the form
    $available_tables = data_source_manager::get_available_tables();

    // Filter operators
    $filter_operators = [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater Than',
        '>=' => 'Greater Than or Equal',
        '<' => 'Less Than',
        '<=' => 'Less Than or Equal',
        'LIKE' => 'Contains',
        'NOT LIKE' => 'Does Not Contain',
        'IN' => 'In List',
        'NOT IN' => 'Not In List',
        'IS NULL' => 'Is Empty',
        'IS NOT NULL' => 'Is Not Empty'
    ];

    // Category options
    $category_options = [
        'users' => 'Users & Accounts',
        'products' => 'Products & Inventory',
        'orders' => 'Orders & Transactions',
        'content' => 'Content & Media',
        'analytics' => 'Analytics & Reports',
        'system' => 'System & Configuration',
        'other' => 'Other'
    ];

    return Edge::render('data-source-builder', [
        'title' => 'Edit Data Source',
        'description' => 'Modify database data source configuration',
        'mode' => 'edit',
        'data_source_id' => $id,
        'data_source' => $data_source,
        'available_tables' => $available_tables,
        'filter_operators' => $filter_operators,
        'category_options' => $category_options,
        'redirect_url' => APP_ROOT . '/data_sources'
    ]);
}

/**
 * Show preview data source view
 */
function preview_view($params) {

    $id = $params['id'] ?? null;
    if (!$id) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source ID is required'
        ]);
    }

    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source not found'
        ]);
    }
    // Get sample data and table info for the preview
    $sample_data = data_source_manager::get_data_source_data($id, ['limit' => 50]);
    $table_info = data_source_manager::get_table_info($data_source['table_name']);

    return Edge::render('data-source-preview', [
        'title' => 'Data Source Preview',
        'description' => 'Preview data from configured data source',
        'data_source' => $data_source,
        'sample_data' => $sample_data,
        'table_info' => $table_info
    ]);
}

/**
 * Get table information fragment for HTMX
 */
function table_info_fragment($params) {
    $table_name = $params['table_name'] ?? '';

    if (empty($table_name)) {
        return '<div class="text-gray-500">No table selected</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        return Edge::render('data-source-table-info', [
            'table_info' => $table_info
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading table information: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get column list fragment for HTMX
 */
function column_list_fragment($params) {
    $table_name = $params['table_name'] ?? '';

    if (empty($table_name)) {
        return '<div class="text-gray-500">No table selected</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $columns = $table['columns'];
                break;
            }
        }

        return Edge::render('data-source-column-list', [
            'columns' => $columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get data preview fragment for HTMX
 */
function data_preview_fragment($params) {
    // Parse all the same data as query_preview_fragment
    $tables = [];
    $joins = [];
    $selected_columns = [];
    $filters = [];

    // Parse selected tables
    if (isset($params['selected_tables'])) {
        $tables = json_decode($params['selected_tables'], true) ?: [];
    } elseif (!empty($params['table_name'])) {
        $tables = [$params['table_name']];
    }

    // Parse joins from form data (same logic as query_preview_fragment)
    if (isset($params['joins']) && is_array($params['joins'])) {
        foreach ($params['joins'] as $join_data) {
            if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                // Format column references properly
                $left_column = $join_data['left_column'];
                $right_column = $join_data['right_column'];

                // If columns don't already have table prefix, add it
                if (strpos($left_column, '.') === false) {
                    $left_column = $join_data['left_table'] . '.' . $left_column;
                }
                if (strpos($right_column, '.') === false) {
                    $right_column = $join_data['right_table'] . '.' . $right_column;
                }

                $joins[] = [
                    'type' => $join_data['type'] ?? 'INNER',
                    'table' => $join_data['right_table'],
                    'left_column' => $left_column,
                    'right_column' => $right_column,
                    'left_table' => $join_data['left_table'],
                    'right_table' => $join_data['right_table'],
                    'left_alias' => $join_data['left_alias'] ?? '',
                    'right_alias' => $join_data['right_alias'] ?? ''
                ];
            }
        }
    }

    // Parse selected columns - handle both array and JSON formats
    $selected_columns_array = [];
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        // Handle array format from form
        $selected_columns_array = $params['selected_columns'];
    } elseif (isset($params['selected_columns_json'])) {
        // Handle JSON format for backward compatibility
        $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
    }

    // Convert array format to table.column format
    foreach ($selected_columns_array as $column) {
        if (strpos($column, '.') !== false) {
            list($table, $col) = explode('.', $column, 2);
            $selected_columns[$table][] = $col;
        }
    }

    // Parse filters from form data
    if (isset($params['filters']) && is_array($params['filters'])) {
        foreach ($params['filters'] as $filter) {
            if (!empty($filter['column']) && !empty($filter['operator'])) {
                $filters[] = [
                    'column' => $filter['column'],
                    'operator' => $filter['operator'],
                    'value' => $filter['value'] ?? ''
                ];
            }
        }
    }

    // Parse table aliases from form data
    $table_aliases = [];
    if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
        foreach ($params['table_aliases'] as $table_name => $alias) {
            if (!empty($alias) && in_array($table_name, $tables)) {
                $table_aliases[$table_name] = trim($alias);
            }
        }
    }

    // Parse column aliases from form data
    $column_aliases = [];
    if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
        foreach ($params['column_aliases'] as $column_key => $alias) {
            if (!empty($alias) && strpos($column_key, '.') !== false) {
                list($table, $column) = explode('.', $column_key, 2);
                if (in_array($table, $tables)) {
                    $column_aliases[$column_key] = trim($alias);
                }
            }
        }
    }

    // Parse custom columns from form data
    $custom_columns = [];
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $custom_columns[] = [
                    'sql' => trim($custom_column['sql']),
                    'alias' => trim($custom_column['alias'])
                ];
            }
        }
    }

    // Parse sorting from form data
    $sorting = [];
    if (isset($params['sorting']) && is_array($params['sorting'])) {
        foreach ($params['sorting'] as $sort) {
            if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                $sorting[] = [
                    'column' => trim($sort['column']),
                    'direction' => strtoupper(trim($sort['direction']))
                ];
            }
        }
    }

    // Parse limits from form data
    $limits = [
        'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
        'limit' => '',
        'offset' => ''
    ];
    if ($limits['enabled']) {
        if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
            $limits['limit'] = (int)$params['limits']['limit'];
        }
        if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
            $limits['offset'] = (int)$params['limits']['offset'];
        }
    }

    if (empty($tables)) {
        return '<div class="text-gray-500">No tables selected</div>';
    }

    try {
        // Build the same query as the preview
        $query = build_multi_table_query($tables, $joins, $selected_columns, $filters, $table_aliases, $column_aliases, $custom_columns, $sorting, $limits);

        // Add LIMIT for preview
        $query .= " LIMIT 10";

        // Execute the query directly
        $result = execute_preview_query($query);

        if (!$result['success']) {
            return '<div class="text-red-500">Error loading preview: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '</div>';
        }

        return Edge::render('data-source-preview-table', [
            'data' => $result['data'] ?? [],
            'columns' => !empty($result['data']) ? array_keys($result['data'][0]) : []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading preview: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get filter row fragment for HTMX
 */
function filter_row_fragment($params) {
    $table_name = $params['table_name'] ?? '';
    $filter_index = $params['filter_index'] ?? 0;

    try {
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $columns = $table['columns'];
                break;
            }
        }

        // Filter operators
        $filter_operators = [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'LIKE' => 'Contains',
            'NOT LIKE' => 'Does Not Contain',
            'IN' => 'In List',
            'NOT IN' => 'Not In List',
            'IS NULL' => 'Is Empty',
            'IS NOT NULL' => 'Is Not Empty'
        ];

        return Edge::render('data-source-filter-row', [
            'columns' => $columns,
            'filter_operators' => $filter_operators,
            'filter_index' => $filter_index
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating filter: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove filter row (returns empty content for HTMX)
 */
function remove_filter_row($params) {
    // Simply return empty content to remove the filter row
    return '';
}

/**
 * Test table connection for form builder
 */
function test_table_connection($params) {
    // Get table name from either old single table format or new multi-table format
    $table_name = '';

    if (!empty($params['table_name'])) {
        $table_name = $params['table_name'];
    } elseif (!empty($params['selected_tables'])) {
        $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        $table_name = $selected_tables[0] ?? '';
    }

    if (empty($table_name)) {
        return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">Please select a table first</div></div>';
    }

    try {
        $data = sample_data([
            'table_name' => $table_name,
            'limit' => 1
        ]);

        if ($data['success']) {
            return '<div class="bg-green-50 border border-green-200 rounded-md p-4"><div class="text-green-800">✓ Connection successful! Table is accessible and contains data.</div></div>';
        } else {
            return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">✗ Connection failed: ' . htmlspecialchars($data['error'] ?? 'Unknown error') . '</div></div>';
        }

    } catch (Exception $e) {
        return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">✗ Connection test failed: ' . htmlspecialchars($e->getMessage()) . '</div></div>';
    }
}

/**
 * Generate and display the final SQL query
 */
function query_preview_fragment($params) {
    // Handle both single table (legacy) and multi-table formats
    $tables = [];
    $joins = [];
    $selected_columns = [];
    $filters = [];

    // Parse selected tables
    if (isset($params['selected_tables'])) {
        $tables = json_decode($params['selected_tables'], true) ?: [];
    } elseif (!empty($params['table_name'])) {
        $tables = [$params['table_name']];
    }

    // Parse joins from form data
    if (isset($params['joins']) && is_array($params['joins'])) {
        foreach ($params['joins'] as $join_data) {
            if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                // Format column references properly
                $left_column = $join_data['left_column'];
                $right_column = $join_data['right_column'];

                // If columns don't already have table prefix, add it
                if (strpos($left_column, '.') === false) {
                    $left_column = $join_data['left_table'] . '.' . $left_column;
                }
                if (strpos($right_column, '.') === false) {
                    $right_column = $join_data['right_table'] . '.' . $right_column;
                }

                $joins[] = [
                    'type' => $join_data['type'] ?? 'INNER',
                    'table' => $join_data['right_table'],
                    'left_column' => $left_column,
                    'right_column' => $right_column,
                    'left_table' => $join_data['left_table'],
                    'right_table' => $join_data['right_table'],
                    'left_alias' => $join_data['left_alias'] ?? '',
                    'right_alias' => $join_data['right_alias'] ?? ''
                ];
            }
        }
    } elseif (isset($params['joins']) && is_string($params['joins'])) {
        // Handle JSON string format (for backward compatibility)
        $joins = json_decode($params['joins'], true) ?: [];
    }

    // Parse selected columns - handle both array and JSON formats
    $selected_columns_array = [];
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        // Handle array format from form
        $selected_columns_array = $params['selected_columns'];
    } elseif (isset($params['selected_columns_json'])) {
        // Handle JSON format for backward compatibility
        $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
    }

    // Convert array format to table.column format
    foreach ($selected_columns_array as $column) {
        if (strpos($column, '.') !== false) {
            list($table, $col) = explode('.', $column, 2);
            $selected_columns[$table][] = $col;
        }
    }

    // Parse filters from form data
    if (isset($params['filters']) && is_array($params['filters'])) {
        foreach ($params['filters'] as $filter) {
            if (!empty($filter['column']) && !empty($filter['operator'])) {
                $filters[] = [
                    'column' => $filter['column'],
                    'operator' => $filter['operator'],
                    'value' => $filter['value'] ?? ''
                ];
            }
        }
    }

    // Parse table aliases from form data
    $table_aliases = [];
    if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
        foreach ($params['table_aliases'] as $table_name => $alias) {
            if (!empty($alias) && in_array($table_name, $tables)) {
                $table_aliases[$table_name] = trim($alias);
            }
        }
    }

    // Parse column aliases from form data
    $column_aliases = [];
    if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
        foreach ($params['column_aliases'] as $column_key => $alias) {
            if (!empty($alias) && strpos($column_key, '.') !== false) {
                list($table, $column) = explode('.', $column_key, 2);
                if (in_array($table, $tables)) {
                    $column_aliases[$column_key] = trim($alias);
                }
            }
        }
    }

    // Parse custom columns from form data
    $custom_columns = [];
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $custom_columns[] = [
                    'sql' => trim($custom_column['sql']),
                    'alias' => trim($custom_column['alias'])
                ];
            }
        }
    }

    // Parse sorting from form data
    $sorting = [];
    if (isset($params['sorting']) && is_array($params['sorting'])) {
        foreach ($params['sorting'] as $sort) {
            if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                $sorting[] = [
                    'column' => trim($sort['column']),
                    'direction' => strtoupper(trim($sort['direction']))
                ];
            }
        }
    }

    // Parse limits from form data
    $limits = [
        'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
        'limit' => '',
        'offset' => ''
    ];
    if ($limits['enabled']) {
        if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
            $limits['limit'] = (int)$params['limits']['limit'];
        }
        if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
            $limits['offset'] = (int)$params['limits']['offset'];
        }
    }

    if (empty($tables)) {
        return '<div class="text-gray-500">Select tables to see the query preview</div>';
    }

    try {
        $query = build_multi_table_query($tables, $joins, $selected_columns, $filters, $table_aliases, $column_aliases, $custom_columns, $sorting, $limits);

        return Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $tables),
            'filters' => $filters,
            'tables' => $tables,
            'joins' => $joins,
            'selected_columns' => $selected_columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error generating query: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Build SQL query from table name and filters
 */
function build_query_from_filters($table_name, $filters = []) {
    $query = "SELECT * FROM `{$table_name}`";

    if (!empty($filters)) {
        $where_conditions = [];

        foreach ($filters as $filter) {
            $column = $filter['column'];
            $operator = $filter['operator'];
            $value = $filter['value'];

            switch ($operator) {
                case 'IS NULL':
                    $where_conditions[] = "`{$column}` IS NULL";
                    break;
                case 'IS NOT NULL':
                    $where_conditions[] = "`{$column}` IS NOT NULL";
                    break;
                case 'IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "`{$column}` IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'NOT IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "`{$column}` NOT IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'LIKE':
                    $where_conditions[] = "`{$column}` LIKE '%" . addslashes($value) . "%'";
                    break;
                case 'NOT LIKE':
                    $where_conditions[] = "`{$column}` NOT LIKE '%" . addslashes($value) . "%'";
                    break;
                default:
                    $where_conditions[] = "`{$column}` {$operator} '" . addslashes($value) . "'";
                    break;
            }
        }

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }
    }

    return $query;
}

/**
 * Get table selection fragment for multi-table support
 */
function table_selection_fragment($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $available_tables = data_source_manager::get_available_tables();

    return Edge::render('data-source-table-selection', [
        'available_tables' => $available_tables,
        'selected_tables' => $selected_tables
    ]);
}

/**
 * Add a new table to the data source
 */
function add_table_fragment($params) {
    $table_name = $params['table_name'] ?? '';
    $table_index = $params['table_index'] ?? 0;

    if (empty($table_name)) {
        return '<div class="text-red-500">Please select a table</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        return Edge::render('data-source-table-item', [
            'table_info' => $table_info,
            'table_index' => $table_index,
            'is_primary' => $table_index == 0
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding table: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get join configuration fragment
 */
function join_configuration_fragment($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $existing_joins = json_decode($params['joins'] ?? '[]', true) ?: [];

    if (count($selected_tables) < 2) {
        return '<div class="text-gray-500">Add at least 2 tables to configure joins</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        return Edge::render('data-source-join-configuration', [
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns,
            'existing_joins' => $existing_joins
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading join configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get column selection fragment for multi-table
 */
function column_selection_fragment($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $selected_columns = json_decode($params['selected_columns'] ?? '[]', true) ?: [];

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => $selected_columns,
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading column selection: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Build complex SQL query with joins
 */
function build_multi_table_query($tables, $joins = [], $selected_columns = [], $filters = [], $table_aliases = [], $column_aliases = [], $custom_columns = [], $sorting = [], $limits = []) {
    if (empty($tables)) {
        return "-- No tables selected";
    }

    $primary_table = $tables[0];

    // Build table alias mapping from table aliases and joins
    $all_table_aliases = $table_aliases; // Start with explicit table aliases

    // Add join aliases (join aliases take precedence over table aliases)
    foreach ($joins as $join) {
        if (!empty($join['left_alias'])) {
            $all_table_aliases[$join['left_table']] = $join['left_alias'];
        }
        if (!empty($join['right_alias'])) {
            $all_table_aliases[$join['right_table']] = $join['right_alias'];
        }
    }

    // Build SELECT clause
    $select_parts = [];

    // Add regular columns
    if (!empty($selected_columns)) {
        foreach ($selected_columns as $table_name => $columns) {
            $table_ref = isset($all_table_aliases[$table_name]) ? $all_table_aliases[$table_name] : $table_name;
            foreach ($columns as $column) {
                $column_key = $table_name . '.' . $column;

                // Check if there's a custom column alias
                if (isset($column_aliases[$column_key]) && !empty($column_aliases[$column_key])) {
                    $column_alias = $column_aliases[$column_key];
                    $select_parts[] = "`{$table_ref}`.`{$column}` AS `{$column_alias}`";
                } else {
                    // Use default alias format (table_column)
                    $alias_suffix = isset($all_table_aliases[$table_name]) ? $all_table_aliases[$table_name] : $table_name;
                    $select_parts[] = "`{$table_ref}`.`{$column}` AS `{$alias_suffix}_{$column}`";
                }
            }
        }
    }

    // Add custom columns
    if (!empty($custom_columns)) {
        foreach ($custom_columns as $custom_column) {
            if (!empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $select_parts[] = "({$custom_column['sql']}) AS `{$custom_column['alias']}`";
            }
        }
    }

    // If no columns selected, use *
    $select = empty($select_parts) ? "*" : implode(', ', $select_parts);

    // Build FROM clause with primary table alias if available
    $primary_table_ref = isset($all_table_aliases[$primary_table]) ? "`{$primary_table}` AS `{$all_table_aliases[$primary_table]}`" : "`{$primary_table}`";
    $query = "SELECT {$select} FROM {$primary_table_ref}";

    // Add joins with aliases
    foreach ($joins as $join) {
        $join_type = strtoupper($join['type'] ?? 'INNER');
        $join_table = $join['right_table'] ?? $join['table']; // Support both formats
        $left_column = $join['left_column'];
        $right_column = $join['right_column'];

        // Build table reference with alias if provided
        $join_table_ref = !empty($join['right_alias']) ? "`{$join_table}` AS `{$join['right_alias']}`" : "`{$join_table}`";

        // Format column references with proper backticks and aliases
        $formatted_left = $left_column;
        $formatted_right = $right_column;

        if (strpos($left_column, '.') !== false) {
            $parts = explode('.', $left_column);
            $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
            $formatted_left = "`{$table_ref}`.`{$parts[1]}`";
        }

        if (strpos($right_column, '.') !== false) {
            $parts = explode('.', $right_column);
            $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
            $formatted_right = "`{$table_ref}`.`{$parts[1]}`";
        }

        $query .= " {$join_type} JOIN {$join_table_ref} ON {$formatted_left} = {$formatted_right}";
    }

    // Add WHERE conditions
    if (!empty($filters)) {
        $where_conditions = [];

        foreach ($filters as $filter) {
            $column = $filter['column'];
            $operator = $filter['operator'];
            $value = $filter['value'];

            // Handle table.column format with aliases
            if (strpos($column, '.') === false) {
                $primary_table_ref = isset($all_table_aliases[$primary_table]) ? $all_table_aliases[$primary_table] : $primary_table;
                $column = "`{$primary_table_ref}`.`{$column}`";
            } else {
                $parts = explode('.', $column);
                $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
                $column = "`{$table_ref}`.`{$parts[1]}`";
            }

            switch ($operator) {
                case 'IS NULL':
                    $where_conditions[] = "{$column} IS NULL";
                    break;
                case 'IS NOT NULL':
                    $where_conditions[] = "{$column} IS NOT NULL";
                    break;
                case 'IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "{$column} IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'NOT IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "{$column} NOT IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'LIKE':
                    $where_conditions[] = "{$column} LIKE '%" . addslashes($value) . "%'";
                    break;
                case 'NOT LIKE':
                    $where_conditions[] = "{$column} NOT LIKE '%" . addslashes($value) . "%'";
                    break;
                default:
                    $where_conditions[] = "{$column} {$operator} '" . addslashes($value) . "'";
                    break;
            }
        }

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }
    }

    // Add ORDER BY clause
    if (!empty($sorting)) {
        $order_parts = [];
        foreach ($sorting as $sort) {
            if (!empty($sort['column']) && !empty($sort['direction'])) {
                $column = $sort['column'];
                $direction = strtoupper($sort['direction']);

                // Validate direction
                if (!in_array($direction, ['ASC', 'DESC'])) {
                    $direction = 'ASC';
                }

                $order_parts[] = "`{$column}` {$direction}";
            }
        }

        if (!empty($order_parts)) {
            $query .= " ORDER BY " . implode(', ', $order_parts);
        }
    }

    // Add LIMIT clause
    if (!empty($limits) && ($limits['enabled'] ?? false)) {
        if (!empty($limits['limit']) && is_numeric($limits['limit'])) {
            $limit = (int)$limits['limit'];
            $query .= " LIMIT {$limit}";

            // Add OFFSET if specified
            if (!empty($limits['offset']) && is_numeric($limits['offset'])) {
                $offset = (int)$limits['offset'];
                $query .= " OFFSET {$offset}";
            }
        }
    }

    return $query;
}

/**
 * Remove table (returns empty content for HTMX plus OOB updates)
 */
function remove_table($params) {
    $table_name = $params['table_name'] ?? '';
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Remove the table from selected tables
    $updated_tables = array_filter($selected_tables, function($table) use ($table_name) {
        return $table !== $table_name;
    });
    $updated_tables = array_values($updated_tables); // Re-index array

    try {
        // Generate updated join configuration
        $join_config = Edge::render('data-source-join-configuration', [
            'selected_tables' => $updated_tables,
            'table_columns' => [],
            'existing_joins' => []
        ]);

        // Generate updated column selection
        $table_columns = [];
        foreach ($updated_tables as $table) {
            $table_info = data_source_manager::get_table_info($table);
            if ($table_info) {
                $table_columns[$table] = $table_info['columns'];
            }
        }

        $column_selection = Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => [],
            'column_aliases' => []
        ]);

        // Generate updated query preview
        if (!empty($updated_tables)) {
            $query = build_multi_table_query($updated_tables, [], [], [], [], [], [], [], []);
            $query_preview = Edge::render('data-source-query-preview', [
                'query' => $query,
                'table_name' => implode(', ', $updated_tables),
                'filters' => [],
                'tables' => $updated_tables,
                'joins' => [],
                'selected_columns' => []
            ]);
        } else {
            $query_preview = '<div class="text-center py-8 text-gray-500">Select tables to see the SQL query</div>';
        }

        // Return empty content for the removed table plus OOB swaps
        return '' .
               '<div id="join-configuration-container" hx-swap-oob="innerHTML">' . $join_config . '</div>' .
               '<div id="column-selection-container" hx-swap-oob="innerHTML">' . $column_selection . '</div>' .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Select all columns for all tables
 */
function select_all_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Get all columns for all tables
        $all_columns = [];
        $table_columns = [];

        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
                foreach ($table_info['columns'] as $column) {
                    $all_columns[] = $table_name . '.' . $column['Field'];
                }
            }
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => $all_columns,
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error selecting columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Clear all column selections
 */
function clear_all_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => [],
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error clearing columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Select all columns for a specific table
 */
function select_table_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $target_table = $params['table_name'] ?? '';
    $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

    if (empty($selected_tables) || empty($target_table)) {
        return '<div class="text-gray-500">Invalid request</div>';
    }

    try {
        $table_columns = [];
        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];

                // Add all columns for target table
                if ($table_name === $target_table) {
                    foreach ($table_info['columns'] as $column) {
                        $updated_selected[] = $table_name . '.' . $column['Field'];
                    }
                }
            }
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => $updated_selected,
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error selecting table columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Clear all columns for a specific table
 */
function clear_table_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $target_table = $params['table_name'] ?? '';
    $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

    if (empty($selected_tables) || empty($target_table)) {
        return '<div class="text-gray-500">Invalid request</div>';
    }

    try {
        $table_columns = [];
        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => array_values($updated_selected),
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error clearing table columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Add a new filter row (calculates index server-side)
 */
function add_filter_row($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Calculate filter index by counting existing filters in the form
    $filter_index = 0;
    if (isset($params['filters']) && is_array($params['filters'])) {
        $filter_index = count($params['filters']);
    }

    if (empty($selected_tables)) {
        return '<div class="text-red-500">Please select tables first</div>';
    }

    try {
        // Get columns from the first table (primary table)
        $primary_table = $selected_tables[0];
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $primary_table) {
                $columns = $table['columns'];
                break;
            }
        }

        // Filter operators
        $filter_operators = [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'LIKE' => 'Contains',
            'NOT LIKE' => 'Does Not Contain',
            'IN' => 'In List',
            'NOT IN' => 'Not In List',
            'IS NULL' => 'Is Empty',
            'IS NOT NULL' => 'Is Not Empty'
        ];

        return Edge::render('data-source-filter-row', [
            'columns' => $columns,
            'filter_operators' => $filter_operators,
            'filter_index' => $filter_index,
            'filter' => [
                'column' => '',
                'operator' => '=',
                'value' => ''
            ]
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating filter: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Add a new table (calculates index server-side)
 */
function add_table($params) {
    $table_name = $params['add_table'] ?? '';
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (empty($table_name)) {
        return '<div class="text-red-500">Please select a table</div>';
    }

    if (is_array($selected_tables) && in_array($table_name, $selected_tables)) {
        return '<div class="text-red-500">Table already selected</div>';
    }

    $table_index = count($selected_tables);

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        // Generate the table item
        $table_item = Edge::render('data-source-table-item', [
            'table_info' => $table_info,
            'table_index' => $table_index,
            'is_primary' => $table_index == 0,
            'table_alias' => '' // New tables start with empty alias
        ]);

        // Update selected tables array for dependent sections
        $updated_tables = array_merge($selected_tables, [$table_name]);

        // Generate updated join configuration
        $join_config = Edge::render('data-source-join-configuration', [
            'selected_tables' => $updated_tables,
            'table_columns' => [],
            'existing_joins' => []
        ]);

        // Generate updated column selection
        $table_columns = [];
        foreach ($updated_tables as $table) {
            $table_info_for_columns = data_source_manager::get_table_info($table);
            if ($table_info_for_columns) {
                $table_columns[$table] = $table_info_for_columns['columns'];
            }
        }

        $column_selection = Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => [],
            'column_aliases' => []
        ]);

        // Generate updated query preview
        $query = build_multi_table_query($updated_tables, [], [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $updated_tables),
            'filters' => [],
            'tables' => $updated_tables,
            'joins' => [],
            'selected_columns' => []
        ]);

        // Return main content plus OOB swaps
        return $table_item .
               '<div id="join-configuration-container" hx-swap-oob="innerHTML">' . $join_config . '</div>' .
               '<div id="column-selection-container" hx-swap-oob="innerHTML">' . $column_selection . '</div>' .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding table: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Select all columns with query preview update
 */
function select_all_columns_with_preview($params) {
    $column_selection = select_all_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get all columns for query preview
        $all_columns = [];
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = [];
                foreach ($table_info['columns'] as $column) {
                    $all_columns[] = $table_name . '.' . $column['Field'];
                    $table_columns[$table_name][] = $column['Field'];
                }
            }
        }

        $query = build_multi_table_query($selected_tables, [], $table_columns, [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $table_columns
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Clear all columns with query preview update
 */
function clear_all_columns_with_preview($params) {
    $column_selection = clear_all_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        $query = build_multi_table_query($selected_tables, [], [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => []
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Update column selection and query preview
 */
function update_column_selection($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    // Additional fallback: if selected_tables param exists but is malformed (like just "["), use tables array
    if (empty($selected_tables) && isset($params['selected_tables']) && $params['selected_tables'] === '[') {
        if (isset($params['tables']) && is_array($params['tables'])) {
            $selected_tables = array_values($params['tables']);
        }
    }

    $selected_columns = $params['selected_columns'] ?? [];
    $column_aliases = $params['column_aliases'] ?? [];

    // Debug logging
    tcs_log("update_column_selection - selected_tables param: " . ($params['selected_tables'] ?? 'null'), 'data_source_debug');
    tcs_log("update_column_selection - tables param: " . json_encode($params['tables'] ?? []), 'data_source_debug');
    tcs_log("update_column_selection - final selected_tables: " . json_encode($selected_tables), 'data_source_debug');

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Generate updated column selection
        $column_selection = Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'selected_columns' => $selected_columns,
            'column_aliases' => $column_aliases
        ]);

        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        // Generate updated data preview
        $data_preview = data_preview_fragment($params);

        // Return main content plus OOB swaps for previews
        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="preview-container" hx-swap-oob="innerHTML">' . $data_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating column selection: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update column alias and both previews
 */
function update_column_alias($params) {
    try {
        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        // Generate updated data preview
        $data_preview = data_preview_fragment($params);

        // Return query preview as main content plus OOB swap for data preview
        return $query_preview .
               '<div id="preview-container" hx-swap-oob="innerHTML">' . $data_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating column alias: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Add a new custom column row
 */
function add_custom_column($params) {
    try {
        // Get current custom columns to determine the next index
        $existing_custom_columns = $params['custom_columns'] ?? [];
        $next_index = is_array($existing_custom_columns) ? count($existing_custom_columns) : 0;

        // Create a new custom column with default values
        $new_custom_column = [
            'sql' => '',
            'alias' => ''
        ];

        // Generate the custom column item
        $custom_column_item = Edge::render('data-source-custom-column-item', [
            'custom_column' => $new_custom_column,
            'column_index' => $next_index
        ]);

        return $custom_column_item;

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding custom column: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a custom column row
 */
function remove_custom_column($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Return empty content for the removed custom column plus OOB swap for query preview
        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Add a new sort row
 */
function add_sort_row($params) {
    try {
        // Get current sorting to determine the next index
        $existing_sorting = $params['sorting'] ?? [];
        $next_index = is_array($existing_sorting) ? count($existing_sorting) : 0;

        // Get available columns for the dropdown
        $available_columns = get_available_columns_for_sorting($params);

        // Create a new sort with default values
        $new_sort = [
            'column' => '',
            'direction' => 'ASC'
        ];

        // Generate the sort item
        $sort_item = Edge::render('data-source-sorting-item', [
            'sort' => $new_sort,
            'sort_index' => $next_index,
            'available_columns' => $available_columns
        ]);

        return $sort_item;

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding sort: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a sort row
 */
function remove_sort_row($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Return empty content for the removed sort plus OOB swap for query preview
        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Get available columns for sorting (includes regular columns, aliased columns, and custom columns)
 */
function get_available_columns_for_sorting($params) {
    $available_columns = [];

    // Add regular selected columns
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        foreach ($params['selected_columns'] as $column) {
            // Check if there's a custom alias for this column
            if (isset($params['column_aliases'][$column]) && !empty($params['column_aliases'][$column])) {
                $available_columns[] = $params['column_aliases'][$column];
            } else {
                // Use default alias format
                if (strpos($column, '.') !== false) {
                    list($table, $col) = explode('.', $column, 2);
                    $available_columns[] = $table . '_' . $col;
                } else {
                    $available_columns[] = $column;
                }
            }
        }
    }

    // Add custom columns
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (!empty($custom_column['alias'])) {
                $available_columns[] = $custom_column['alias'];
            }
        }
    }

    return array_unique($available_columns);
}

/**
 * Update query preview based on current form state
 */
function update_query_preview($params) {
    return query_preview_fragment($params);
}

/**
 * Add a new join row
 */
function add_join_row($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $existing_joins = [];

    // Calculate join index by counting existing joins in the form
    $join_index = 0;
    if (isset($params['joins']) && is_array($params['joins'])) {
        $join_index = count($params['joins']);
    }

    if (count($selected_tables) < 2) {
        return '<div class="text-red-500">You need at least 2 tables to create a join</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Create a new join with default values
        $new_join = [
            'type' => 'INNER',
            'left_table' => $selected_tables[0] ?? '',
            'left_column' => '',
            'right_table' => $selected_tables[1] ?? '',
            'right_column' => '',
            'left_alias' => '',
            'right_alias' => ''
        ];

        return Edge::render('data-source-join-item', [
            'join' => $new_join,
            'join_index' => $join_index,
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating join: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove join row (returns empty content for HTMX)
 */
function remove_join_row($params) {
    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get current joins excluding the one being removed
        $current_joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            $remove_index = $params['join_index'] ?? -1;
            foreach ($params['joins'] as $index => $join_data) {
                if ($index != $remove_index && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    // Format column references properly
                    $left_column = $join_data['left_column'];
                    $right_column = $join_data['right_column'];

                    // If columns don't already have table prefix, add it
                    if (strpos($left_column, '.') === false) {
                        $left_column = $join_data['left_table'] . '.' . $left_column;
                    }
                    if (strpos($right_column, '.') === false) {
                        $right_column = $join_data['right_table'] . '.' . $right_column;
                    }

                    $current_joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'table' => $join_data['right_table'],
                        'left_column' => $left_column,
                        'right_column' => $right_column,
                        'left_table' => $join_data['left_table'],
                        'right_table' => $join_data['right_table'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        $query = build_multi_table_query($selected_tables, $current_joins, [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => $current_joins,
            'selected_columns' => []
        ]);

        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return '';
}

/**
 * Update join columns when table selection changes
 */
function update_join_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $join_index = $params['join_index'] ?? 0;
    $field = $params['field'] ?? '';

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Get current join data from form
        $current_join = [
            'type' => $params['joins'][$join_index]['type'] ?? 'INNER',
            'left_table' => $params['joins'][$join_index]['left_table'] ?? '',
            'left_column' => $params['joins'][$join_index]['left_column'] ?? '',
            'right_table' => $params['joins'][$join_index]['right_table'] ?? '',
            'right_column' => $params['joins'][$join_index]['right_column'] ?? '',
            'left_alias' => $params['joins'][$join_index]['left_alias'] ?? '',
            'right_alias' => $params['joins'][$join_index]['right_alias'] ?? ''
        ];

        // Update the changed field
        if ($field === 'left_table') {
            $current_join['left_table'] = $params['joins'][$join_index]['left_table'] ?? '';
            $current_join['left_column'] = ''; // Reset column when table changes
        } elseif ($field === 'right_table') {
            $current_join['right_table'] = $params['joins'][$join_index]['right_table'] ?? '';
            $current_join['right_column'] = ''; // Reset column when table changes
        }

        return Edge::render('data-source-join-item', [
            'join' => $current_join,
            'join_index' => $join_index,
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating join: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Select table columns with query preview update
 */
function select_table_columns_with_preview($params) {
    $column_selection = select_table_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get updated selected columns from the result
        $target_table = $params['table_name'] ?? '';
        $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

        // Remove old selections for this table and add new ones
        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        // Add all columns for target table
        $table_info = data_source_manager::get_table_info($target_table);
        if ($table_info) {
            foreach ($table_info['columns'] as $column) {
                $updated_selected[] = $target_table . '.' . $column['Field'];
            }
        }

        // Convert to table.column format for query building
        $selected_columns = [];
        foreach ($updated_selected as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        $query = build_multi_table_query($selected_tables, [], $selected_columns, [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $selected_columns
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Clear table columns with query preview update
 */
function clear_table_columns_with_preview($params) {
    $column_selection = clear_table_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get updated selected columns (with target table columns removed)
        $target_table = $params['table_name'] ?? '';
        $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        // Convert to table.column format for query building
        $selected_columns = [];
        foreach ($updated_selected as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        $query = build_multi_table_query($selected_tables, [], $selected_columns, [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $selected_columns
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Format SQL query for human-readable display
 */
function format_sql_query($query) {
    if (empty($query)) {
        return $query;
    }

    // Remove extra whitespace and normalize
    $query = preg_replace('/\s+/', ' ', trim($query));

    // Define SQL keywords that should start new lines
    $keywords = [
        'SELECT', 'FROM', 'WHERE', 'ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT',
        'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN', 'JOIN',
        'UNION', 'UNION ALL', 'AND', 'OR'
    ];

    // Sort keywords by length (longest first) to avoid partial matches
    usort($keywords, function($a, $b) {
        return strlen($b) - strlen($a);
    });

    $formatted = $query;

    // Add line breaks before major keywords
    foreach ($keywords as $keyword) {
        $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i';

        if ($keyword === 'AND' || $keyword === 'OR') {
            // Special handling for AND/OR - add line break and indent
            $formatted = preg_replace($pattern, "\n  $keyword", $formatted);
        } elseif (strpos($keyword, 'JOIN') !== false) {
            // JOIN keywords get their own line with slight indent
            $formatted = preg_replace($pattern, "\n$keyword", $formatted);
        } else {
            // Other keywords start at the beginning of the line
            $formatted = preg_replace($pattern, "\n$keyword", $formatted);
        }
    }

    // Clean up the formatting
    $lines = explode("\n", $formatted);
    $formatted_lines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line)) {
            // Add proper indentation
            if (preg_match('/^(AND|OR)\b/i', $line)) {
                $formatted_lines[] = '  ' . $line;
            } elseif (preg_match('/^(INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|JOIN)\b/i', $line)) {
                $formatted_lines[] = $line;
            } else {
                $formatted_lines[] = $line;
            }
        }
    }

    // Join lines and add proper spacing
    $result = implode("\n", $formatted_lines);

    // Add extra spacing around major sections
    $result = preg_replace('/\n(FROM|WHERE|ORDER BY|GROUP BY|HAVING|LIMIT)\b/i', "\n\n$1", $result);

    // Clean up any double newlines at the start
    $result = ltrim($result, "\n");

    return $result;
}

/**
 * Execute a preview query and return formatted results
 */
function execute_preview_query($query) {
    try {
        // Execute the query using the database class
        $result = \system\database::rawQuery($query);

        if (!$result) {
            return [
                'success' => false,
                'error' => 'Query execution failed'
            ];
        }

        // Fetch all results
        $data = $result->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'data' => $data,
            'count' => count($data)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data source info for HTMX selector
 */
function source_info($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? '';

        if (empty($data_source_id)) {
            return ''; // Return empty for no selection
        }

        // Handle "new:" prefixed selections
        if (str_starts_with($data_source_id, 'new:')) {
            $table_name = substr($data_source_id, 4);
            $available_tables = data_source_manager::get_available_tables();

            foreach ($available_tables as $table) {
                if ($table['name'] === $table_name) {
                    return '<div class="mt-2 p-3 bg-gray-50 rounded-md">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">New: ' . htmlspecialchars($table['display_name']) . '</p>
                                        <p class="text-sm text-gray-500">' . htmlspecialchars($table['description']) . '</p>
                                        <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                            <span>Table: <span class="font-mono">' . htmlspecialchars($table['name']) . '</span></span>
                                            <span>Category: <span class="capitalize">' . htmlspecialchars($table['category']) . '</span></span>
                                            <span>Rows: <span>' . number_format($table['row_count']) . '</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>';
                }
            }
            return '';
        }

        // Get existing data source
        $data_source = data_source_manager::get_data_source($data_source_id);
        if (!$data_source) {
            return '';
        }

        return '<div class="mt-2 p-3 bg-gray-50 rounded-md">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">' . htmlspecialchars($data_source['name']) . '</p>
                            <p class="text-sm text-gray-500">' . htmlspecialchars($data_source['description'] ?? '') . '</p>
                            <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                <span>Table: <span class="font-mono">' . htmlspecialchars($data_source['table_name']) . '</span></span>
                                <span>Category: <span class="capitalize">' . htmlspecialchars($data_source['category'] ?? 'other') . '</span></span>
                            </div>
                        </div>
                    </div>
                </div>';

    } catch (Exception $e) {
        return '<div class="mt-2 p-3 bg-red-50 rounded-md text-red-600 text-sm">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get preview data for HTMX data preview
 */
function preview_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? '';
        $limit = (int)($params['limit'] ?? 5);

        if (empty($data_source_id) || str_starts_with($data_source_id, 'new:')) {
            return '<div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                        </div>
                        <div class="border rounded-md overflow-hidden">
                            <div class="p-4 text-center text-gray-500">
                                No preview available for new data sources
                            </div>
                        </div>
                    </div>';
        }

        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        if (!$result['success'] || empty($result['data'])) {
            return '<div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                            <button type="button"
                                    class="text-sm text-indigo-600 hover:text-indigo-500"
                                    hx-get="' . APP_ROOT . '/api/data_sources/preview_data"
                                    hx-vals=\'{"data_source_id": "' . htmlspecialchars($data_source_id) . '", "limit": ' . $limit . '}\'
                                    hx-target="#data_preview_container"
                                    hx-swap="innerHTML"
                                    hx-indicator="#preview_loading">
                                Refresh
                            </button>
                        </div>
                        <div class="border rounded-md overflow-hidden">
                            <div class="p-4 text-center text-gray-500">
                                ' . htmlspecialchars($result['error'] ?? 'No data available') . '
                            </div>
                        </div>
                    </div>';
        }

        $data = $result['data'];
        $columns = !empty($data) ? array_keys($data[0]) : [];

        $table_html = '';
        if (!empty($columns)) {
            $table_html = '<table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>';

            foreach ($columns as $column) {
                $table_html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">' . htmlspecialchars($column) . '</th>';
            }

            $table_html .= '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';

            foreach ($data as $row) {
                $table_html .= '<tr>';
                foreach ($columns as $column) {
                    $value = $row[$column] ?? '';
                    if (is_string($value) && strlen($value) > 50) {
                        $value = substr($value, 0, 50) . '...';
                    }
                    $table_html .= '<td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">' . htmlspecialchars($value) . '</td>';
                }
                $table_html .= '</tr>';
            }

            $table_html .= '</tbody></table>';
        }

        return '<div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                        <button type="button"
                                class="text-sm text-indigo-600 hover:text-indigo-500"
                                hx-get="' . APP_ROOT . '/api/data_sources/preview_data"
                                hx-vals=\'{"data_source_id": "' . htmlspecialchars($data_source_id) . '", "limit": ' . $limit . '}\'
                                hx-target="#data_preview_container"
                                hx-swap="innerHTML"
                                hx-indicator="#preview_loading">
                            Refresh
                        </button>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <div id="preview_loading" class="htmx-indicator p-4 text-center text-gray-500">
                            <svg class="animate-spin h-5 w-5 mx-auto mb-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading preview...
                        </div>
                        <div class="overflow-x-auto">
                            ' . $table_html . '
                        </div>
                    </div>
                </div>';

    } catch (Exception $e) {
        return '<div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <div class="p-4 text-center text-red-500">
                            Error: ' . htmlspecialchars($e->getMessage()) . '
                        </div>
                    </div>
                </div>';
    }
}

/**
 * Create modal for new data source
 */
function create_modal($params = []) {
    try {
        $available_tables = data_source_manager::get_available_tables();

        $modal_html = '<div class="fixed inset-0 z-50 overflow-y-auto">
                        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                            </div>

                            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                                <form hx-post="' . APP_ROOT . '/api/data_sources/create_data_source_htmx"
                                      hx-target="#create_modal_container"
                                      hx-swap="innerHTML">
                                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                            Create New Data Source
                                        </h3>

                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Table</label>
                                                <select name="table_name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                                    <option value="">Select a table...</option>';

        foreach ($available_tables as $table) {
            $modal_html .= '<option value="' . htmlspecialchars($table['name']) . '">' . htmlspecialchars($table['display_name']) . ' (' . number_format($table['row_count']) . ' rows)</option>';
        }

        $modal_html .= '                        </select>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                                <input type="text"
                                                       name="name"
                                                       required
                                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                       placeholder="Enter data source name">
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                                <textarea name="description"
                                                          rows="3"
                                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                          placeholder="Optional description"></textarea>
                                            </div>

                                            <input type="hidden" name="category" value="other">
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                        <button type="submit"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                                            Create
                                        </button>
                                        <button type="button"
                                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                                hx-get="' . APP_ROOT . '/api/data_sources/close_modal"
                                                hx-target="#create_modal_container"
                                                hx-swap="innerHTML">
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>';

        return $modal_html;

    } catch (Exception $e) {
        return '<div class="p-4 text-red-600">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Create data source via HTMX form submission
 */
function create_data_source_htmx($params) {
    try {
        // Validate required fields
        $required_fields = ['name', 'table_name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Prepare data source configuration
        $config = [
            'name' => $params['name'],
            'table_name' => $params['table_name'],
            'description' => $params['description'] ?? '',
            'category' => $params['category'] ?? 'other',
            'column_mapping' => $params['column_mapping'] ?? [],
            'filters' => $params['filters'] ?? []
        ];

        $data_source_id = data_source_manager::create_data_source($config);

        // Trigger page refresh and close modal
        header('HX-Trigger: {"dataSourceCreated": {"id": ' . $data_source_id . ', "message": "Data source created successfully"}}');

        return '<div class="text-center p-4">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>
                    <p class="text-sm text-gray-600 mb-4">Data source created successfully</p>
                </div>';

    } catch (Exception $e) {
        return '<div class="text-center p-4">
                    <div class="text-red-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error</h3>
                    <p class="text-sm text-gray-600 mb-4">' . htmlspecialchars($e->getMessage()) . '</p>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-get="' . APP_ROOT . '/api/data_sources/close_modal"
                            hx-target="#create_modal_container"
                            hx-swap="innerHTML">
                        Close
                    </button>
                </div>';
    }
}

/**
 * Close modal - returns empty content
 */
function close_modal($params = []) {
    header('HX-Trigger: closeDataSourceModal');
    return '';
}

