@props([
    'available_tables' => [],
    'selected_tables' => [],
    'table_aliases' => []
])

<div class="space-y-4">
    <!-- Add Table Section -->
    <div class="flex items-center space-x-4">
        <div class="flex-1">
            @php
                $table_options = ['' => 'Select a table to add...'];
                foreach ($available_tables as $table) {
                    if (!in_array($table['name'], $selected_tables)) {
                        $table_options[$table['name']] = $table['display_name'] . ' (' . number_format($table['row_count']) . ' rows)';
                    }
                }
            @endphp
            <x-forms-select 
                id="add-table-select"
                name="add_table"
                :options="$table_options"
                class_suffix="text-sm"
            />
        </div>
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/add_table"
                hx-target="#selected-tables-container"
                hx-swap="beforeend"
                hx-include="#add-table-select, #selected-tables-input"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Table
        </button>
    </div>
    
    <!-- Selected Tables List -->
    <div id="selected-tables-container" class="space-y-3">
        @if(empty($selected_tables))
            <div class="text-center py-6 text-gray-500" id="no-tables-message">
                No tables selected. Add tables to create your data source.
            </div>
        @else
            @foreach($selected_tables as $index => $table_name)
                @php
                    $table_info = null;
                    foreach ($available_tables as $table) {
                        if ($table['name'] === $table_name) {
                            $table_info = $table;
                            break;
                        }
                    }
                @endphp
                @if($table_info)
                    <x-data-source-table-item
                        :table_info="$table_info"
                        :table_index="$index"
                        :is_primary="$index === 0"
                        :table_alias="$table_aliases[$table_name] ?? ''"
                    />
                @endif
            @endforeach
        @endif
    </div>
    
    <!-- Hidden input to store selected tables -->
    @php
        $selected_tables_json = is_array($selected_tables) ? json_encode($selected_tables) : $selected_tables;
    @endphp
    <input type="hidden" name="selected_tables" id="selected-tables-input" value="{{ $selected_tables_json }}">
</div>

<script>
// Initialize selected tables input on page load
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the DOM to be fully ready
    setTimeout(function() {
        updateSelectedTablesInput();
    }, 100);
});

// Update selected tables when a table is added
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.target.id === 'selected-tables-container') {
        updateSelectedTablesInput();

        // Clear the add table select
        const addSelect = document.getElementById('add-table-select');
        if (addSelect) {
            addSelect.value = '';
        }

        // Hide no tables message
        const noTablesMessage = document.getElementById('no-tables-message');
        const selectedTables = Array.from(document.querySelectorAll('#selected-tables-container [name^="tables["]'));
        if (noTablesMessage && selectedTables.length > 0) {
            noTablesMessage.style.display = 'none';
        }
    }
});

function updateSelectedTablesInput() {
    try {
        // Update the selected tables input
        const tableItems = document.querySelectorAll('#selected-tables-container [name^="tables["]');
        const selectedTables = Array.from(tableItems).map(input => input.value).filter(value => value);
        const selectedTablesInput = document.getElementById('selected-tables-input');
        if (selectedTablesInput) {
            selectedTablesInput.value = JSON.stringify(selectedTables);
            console.log('Updated selected_tables input:', selectedTables);
        }
    } catch (error) {
        console.error('Error updating selected tables input:', error);
    }
}
</script>


